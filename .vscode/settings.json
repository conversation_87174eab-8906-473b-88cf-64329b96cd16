{"python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.testing.autoTestDiscoverOnSaveEnabled": true, "python.envFile": "${workspaceFolder}/app/.env", "coverage-gutters.xmlname": "coverage.xml", "coverage-gutters.coverageFileNames": ["coverage.xml"], "azureFunctions.deploySubpath": "app/durable_functions", "azureFunctions.scmDoBuildDuringDeployment": true, "azureFunctions.pythonVenv": ".venv", "azureFunctions.projectLanguage": "Python", "azureFunctions.projectRuntime": "~4", "debug.internalConsoleOptions": "neverOpen", "azureFunctions.projectLanguageModel": 2, "azureFunctions.projectSubpath": "app/durable_functions"}