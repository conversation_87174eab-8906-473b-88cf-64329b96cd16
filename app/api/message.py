import datetime
import logging
from typing import Annotated
from uuid import UUID

from fastapi import APIRouter, File, Form, HTTPException, UploadFile, status

from constants.operation_ids import operation_ids
from dependencies import ConversationMessageServiceDep
from exceptions import EntityNotFoundError, MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded
from schemas import BaseMessageSerializer, CombinedMessageSerializer, MessageSerializer, Option


__all__ = ['router']

logger = logging.getLogger(__name__)

router = APIRouter(prefix='/messages')


@router.post(
    '',
    operation_id=operation_ids.message.CREATE,
    status_code=status.HTTP_201_CREATED,
)
async def create(
    message_service: ConversationMessageServiceDep,
    conversation_id: Annotated[UUID, Form()],
    content: Annotated[str, Form()] = '',
    selected_option: Annotated[Option | None, Form(description='Selected option as an object')] = None,
    files: Annotated[list[UploadFile] | None, File(description='Optional files to attach to the message')] = None,
) -> CombinedMessageSerializer:
    """
    Create a new message for an existing conversation.

    Parameters:
        message_service: Injected message service dependency
        conversation_id: UUID of the conversation to add the message to
        content: Text content of the message (optional)
        selected_option: The selected option
        files: List of files to attach to the message (optional)

    Returns:
        CombinedMessageSerializer containing both the created user message
        and a system message with expected entity information

    Raises:
        HTTPException:
            - 404 if conversation not found
            - 400 for validation errors or file size/count limits
            - 500 for server errors

    Notes:
        - If files are provided without content, message_type will be set to FILE
        - Files are stored before the response is returned and processed then in a parallel task
    """
    try:
        return await message_service.create(
            conversation_id=conversation_id,
            content=content,
            selected_option=selected_option,
            files=files,
        )
    except EntityNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded, ValueError) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error('Error creating message: %s', e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail='Failed to create message')


@router.get(
    '/{message_id}',
    operation_id=operation_ids.message.GET,
    response_model=MessageSerializer,
)
async def get(
    message_id: UUID,
    message_service: ConversationMessageServiceDep,
) -> BaseMessageSerializer:
    """
    Get a conversation message by its ID.
    """
    try:
        return await message_service.get(message_id)
    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error('Error retrieving message: %s', e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='Failed to retrieve message',
        )


@router.put('/{message_id}/data_processing', status_code=status.HTTP_200_OK)
async def data_processing_endpoint(
    message_id: UUID,
    data: dict,
):
    """
    Temporary endpoint for testing durable functions - logs incoming data.
    """
    logger.info(f'Received data processing request for message {message_id}')
    logger.info(f'Incoming data: {data}')

    # You can add any additional logic here if needed
    return {'status': 'success', 'timestamp': datetime.datetime.now().isoformat()}
