import sqlalchemy as sa
from sqlalchemy.orm import relationship

from core.db import Base


__all__ = ['QualConversation']


class QualConversation(Base):
    __tablename__ = 'QualConversation'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    PublicId = sa.Column(sa.Uuid, unique=True, nullable=False, server_default=sa.text('NEWID()'))
    QualId = sa.Column(sa.String, nullable=False)
    FromDash = sa.Column(sa.Boolean, nullable=False)
    IsCompleted = sa.Column(sa.Boolean, nullable=False)
    CreatedAt = sa.Column(sa.DateTime, server_default=sa.func.now(), nullable=False)
    CreatedById = sa.Column(sa.Uuid, nullable=False)
    CreatedByName = sa.Column(sa.String, nullable=True)

    Messages = relationship('QualConversationMessage', back_populates='Conversation')
    ExtractedData = relationship('QualExtractedData', back_populates='Conversation')
