import logging
from typing import Any, cast

from backoff import expo, on_exception, types
from fastapi import status
import httpx
from httpx import HTTPStatusError, TransportError

from config import settings


__all__ = ['expo_backoff']


logger = logging.getLogger(__name__)


def _log_backoff_retry(details: types.Details) -> None:
    """Log retry attempts."""
    # Cast the Details object to a dictionary
    details_dict = cast(dict[str, Any], details)

    wait = details_dict['wait']
    tries = details_dict['tries']
    target_func = details_dict['target'].__qualname__
    args = details_dict['args']
    logger.warning('Backing off %s for %.1fs after %d tries. Args: %s', target_func, wait, tries, args)


def _should_retry_exception(exception: Exception) -> bool:
    """
    Determine if we should retry for a given exception.

    Args:
        exception: The exception to check

    Returns:
        bool: True if we should retry, False otherwise
    """
    if isinstance(exception, HTTPStatusError):
        # Retry on specific HTTP status codes
        status_code = exception.response.status_code
        return status_code in {
            status.HTTP_408_REQUEST_TIMEOUT,
            status.HTTP_429_TOO_MANY_REQUESTS,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_502_BAD_GATEWAY,
            status.HTTP_503_SERVICE_UNAVAILABLE,
            status.HTTP_504_GATEWAY_TIMEOUT,
        }

    # Retry on transport errors (connection issues, etc.)
    return isinstance(exception, TransportError)


expo_backoff = on_exception(
    expo,
    (httpx.HTTPStatusError, httpx.TransportError),
    max_tries=settings.http_client.backoff.max_retries,
    giveup=lambda e: not _should_retry_exception(e),
    on_backoff=_log_backoff_retry,
    base=settings.http_client.backoff.backoff_base,
    factor=settings.http_client.backoff.backoff_factor,
)
