import json
import logging
from uuid import UUID

from azure.core.exceptions import ResourceExistsError
from azure.storage.queue import BinaryBase64DecodePolicy, BinaryBase64EncodePolicy, QueueMessage
from azure.storage.queue.aio import QueueClient


__all__ = ['DocumentQueueRepository']

logger = logging.getLogger(__name__)


class DocumentQueueRepository:
    def __init__(self, connection_string: str, queue_name: str) -> None:
        self.queue_client = QueueClient.from_connection_string(
            connection_string,
            queue_name,
            message_encode_policy=BinaryBase64EncodePolicy(),
            message_decode_policy=BinaryBase64DecodePolicy(),
        )
        self.queue_name = queue_name
        self._initialized = False

    async def initialize(self) -> None:
        if self._initialized:
            return
        try:
            self._initialized = True
            await self.queue_client.create_queue()
        except ResourceExistsError:
            logger.debug("Queue '%s' already exists", self.queue_name)

    async def send_document_message(self, blob_url: str, signalr_user_id: UUID) -> QueueMessage:
        """
        Sends a message containing the blob_url and signalr_user_id to the document processing queue.

        Args:
            blob_url: URL of the uploaded blob
            signalr_user_id: User ID for SignalR notifications

        Returns:
            The queue message that was sent
        """
        message_content = {'blob_url': blob_url}

        if signalr_user_id:
            message_content['signalr_user_id'] = str(signalr_user_id)

        message_json = json.dumps(message_content)
        message_bytes = message_json.encode('utf-8')
        return await self.queue_client.send_message(message_bytes)
