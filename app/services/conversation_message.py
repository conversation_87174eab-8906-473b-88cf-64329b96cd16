import datetime as dt
import logging
from typing import Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from constants.message import MessageR<PERSON>, MessageType, OptionType
from exceptions import EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository
from schemas import (
    BaseMessageSerializer,
    ClientNameOption,
    CombinedMessageSerializer,
    DatePickerOption,
    DocumentCreationRequest,
    LDMFCountryOption,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)

from .document import DocumentService
from .kx_dash import KXDashService


__all__ = ['ConversationMessageService']

logger = logging.getLogger(__name__)


class ConversationMessageService:
    """Service for conversation message-related business logic."""

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        kx_dash_service: KXDashService,
    ):
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.kx_dash_service = kx_dash_service

    async def create(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
    ) -> CombinedMessageSerializer:
        """
        Create a new conversation message with attached files.

        Args:
            conversation_id: UUID of the conversation
            content: Text content of the message
            selected_option: The selected option
            files: Optional list of files to attach

        Returns:
            Response containing both user message and system message with expected entity

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            MaximumDocumentsNumberExceeded: If too many documents are attached
            MaximumDocumentsSizeExceeded: If documents exceed size limit
            ValueError: If file validation fails
        """
        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
        )

        if selected_option:
            match selected_option.type:
                case OptionType.CLIENT_NAME:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
                case OptionType.LDMF_COUNTRY:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
                case OptionType.DATES:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
                case OptionType.KX_DASH_TASK:
                    system_message = await self.kx_dash_service.on_select(selected_option, conversation_id)
                case _:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
        else:
            # TODO: Remove this once we have a real system message
            system_message = self._get_system_message_with_mocked_result(user_message)

        response = CombinedMessageSerializer(
            user=cast(UserMessageSerializer, await self.create_message(user_message)),
            system=cast(SystemMessageSerializer, await self.create_message(system_message)),
        )

        if files:
            document_data = DocumentCreationRequest(
                conversation_id=user_message.conversation_id,
                files=files,
                message_id=response.user.id,
            )
            response.files = await self.document_service.create_many(document_data)

        return response

    async def create_message(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message.

        Args:
            message_data: Data for creating the message

        Returns:
            Response with the created message data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error creating the message
        """
        logger.debug('Creating new message for conversation ID: %s', message_data.conversation_id)
        try:
            return await self.conversation_message_repository.create(message_data)

        except Exception as e:
            logger.error('Error creating message: %s', e)
            raise

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its ID.

        Args:
            public_id: The ID of the message

        Returns:
            The message response

        Raises:
            EntityNotFoundError: If the message doesn't exist
            DatabaseException: If there's an error retrieving the message
        """
        try:
            logger.debug('Retrieving message with ID: %s', public_id)
            return await self.conversation_message_repository.get(public_id)

        except Exception as e:
            logger.error('Error retrieving message: %s', e)
            raise

    async def list(self, public_id: UUID) -> Sequence[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        try:
            logger.debug('Retrieving all messages for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.list(public_id)

        except Exception as e:
            logger.error('Error retrieving messages for conversation %s: %s', public_id, e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None

        Raises:
            DatabaseException: If there's an error deleting the messages
        """
        try:
            logger.debug('Deleting all messages for conversation with ID: %s', conversation_id)
            await self.conversation_message_repository.delete_many(conversation_id)
        except Exception as e:
            logger.error('Error deleting messages for conversation %s: %s', conversation_id, e)
            raise

    def _get_system_message_with_mocked_result(self, message_data: MessageValidator) -> MessageValidator:
        """
        Create a system message with an expected entity based on the message content.
        """
        # Determine expected entity based on content
        content = message_data.content.lower()
        if 'date' in content:
            options = [
                DatePickerOption(
                    start_date=dt.date.fromisoformat('2025-05-01'), end_date=dt.date.fromisoformat('2025-07-01')
                ),
            ]
        elif 'country' in content:
            options = [
                LDMFCountryOption(ldmf_country='Austria'),
                LDMFCountryOption(ldmf_country='France'),
            ]
        elif 'client' in content:
            options = [
                ClientNameOption(client_name='FashionForward'),
                ClientNameOption(client_name='FashionForward GMBH'),
            ]
        else:
            options = []

        return MessageValidator(
            conversation_id=message_data.conversation_id,
            role=MessageRole.SYSTEM,
            type=message_data.type,
            content=message_data.content,
            options=options,
        )
