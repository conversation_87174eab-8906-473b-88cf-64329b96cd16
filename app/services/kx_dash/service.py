import logging
from typing import Any, Sequence
from uuid import UUID

from constants.message import MessageRole, MessageType
from exceptions import EntityNotFoundError
from repositories import KXDashRepository
from schemas import DashTaskResponse, DataSourceType, KXDashTaskOption, MessageValidator

from ..extracted_data import ExtractedDataService
from .formatter import kx_dash_message_formatter


__all__ = ['KXDashService']


logger = logging.getLogger(__name__)


class KXDashService:
    """Service for KX Dash API interactions and business logic."""

    _ATTRIBUTES_TO_EXTRACT = (
        'client_name',
        'activity_name',
        'member_firm',
        'country',
        'engagement_start_date',
        'engagement_end_date',
        'engagement_lep_emails',
        'engagement_manager_emails',
        'global_business',
        'global_business_service_area',
        'global_business_service_line',
        'global_industry',
        'global_industry_sector',
        'global_lcsp_emails',
    )

    def __init__(
        self,
        kx_dash_repository: KXDashRepository,
        extracted_data_service: ExtractedDataService,
    ):
        """
        Initialize the KX Dash Service with repositories.

        Args:
            kx_dash_repository: Repository for KX Dash API operations
            extracted_data_service: Service for extracted data operations
        """
        self.kx_dash_repository = kx_dash_repository
        self.extracted_data_service = extracted_data_service

    async def list(self) -> Sequence[DashTaskResponse]:
        """
        Get a list of activities matching the query.

        Returns:
            List of DashTaskResponse objects

        Raises:
            Exception: If there's an error fetching activities
        """
        try:
            logger.debug('Fetching activities')
            result = await self.kx_dash_repository.list()

            if not result:
                raise EntityNotFoundError('Activities', 'No activities found')

            return tuple(DashTaskResponse.model_validate(activity) for activity in result)
        except Exception as e:
            logger.error('Error fetching activities: %s', e)
            raise

    async def get(self, activity_id: int) -> DashTaskResponse:
        """
        Get an activity by its ID.

        Args:
            activity_id: The ID of the activity

        Returns:
            DashTaskResponse object

        Raises:
            EntityNotFoundError: If the activity doesn't exist
            Exception: If there's an error fetching the activity
        """
        try:
            logger.debug('Fetching activity with ID: %s', activity_id)
            result = await self.kx_dash_repository.get(activity_id)

            if not result:
                raise EntityNotFoundError('Activity', str(activity_id))

            return DashTaskResponse.model_validate(result)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error('Error fetching activity: %s', e)
            raise

    async def on_select(
        self,
        selected_option: KXDashTaskOption,
        conversation_id: UUID,
    ) -> MessageValidator:
        """
        Select an activity and create a system message in the conversation.

        Args:
            selected_option: ...
            conversation_id: The ID of the conversation

        Returns:
            Response with conversation, message, and activity data

        Raises:
            EntityNotFoundError: If the activity or conversation doesn't exist
            Exception: If there's an error selecting the activity
        """
        try:
            activity = await self.get(selected_option.activity_id)
            activity_details = self._extract_activity_details(activity)
            await self.extracted_data_service.update(
                conversation_id=conversation_id,
                raw_data=activity_details,
                source_type=DataSourceType.KX_DASH,
            )
            message_data = MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=kx_dash_message_formatter(activity_details),
                selected_option=selected_option,
            )
            return message_data

        except EntityNotFoundError:
            raise

        except Exception as e:
            logger.error('Error selecting activity: %s', e)
            raise

    @classmethod
    def _extract_activity_details(cls, activity: DashTaskResponse) -> dict[str, Any]:
        """
        Extract the details from the dash task response.

        Args:
            activity: The dash task response object

        Returns:
            Dictionary with extracted attributes
        """
        activity_data = activity.model_dump(exclude_none=True)
        return {attr: val for attr in cls._ATTRIBUTES_TO_EXTRACT if (val := activity_data.get(attr)) is not None}
