import logging
from typing import cast
from uuid import UUID

from fastapi import UploadFile

from config import settings
from exceptions import EntityNotFoundError, MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded
from repositories import DocumentBlobRepository, DocumentDbRepository, DocumentQueueRepository
from schemas import DocumentCreationRequest, DocumentResponse


__all__ = ['DocumentService']

logger = logging.getLogger(__name__)


class DocumentService:
    """Service for document-related business logic."""

    def __init__(
        self,
        document_db_repository: DocumentDbRepository,
        document_blob_repository: DocumentBlobRepository,
        document_queue_repository: DocumentQueueRepository,
    ):
        self.document_db_repository = document_db_repository
        self.document_blob_repository = document_blob_repository
        self.document_queue_repository = document_queue_repository

    async def create_many(self, document_data: DocumentCreationRequest) -> list[DocumentResponse]:
        """
        Create multiple document records for an existing message and upload files to blob storage.

        Args:
            document_data: Data for creating the documents

        Returns:
            List of responses with the created document data

        Raises:
            MaximumDocumentsNumberExceeded: If there are too many documents
            MaximumDocumentsSizeExceeded: if the total size exceeds the limit
            EntityNotFoundError: If the conversation doesn't exist
            Exception: If there's an error creating the documents or uploading the files
        """
        try:
            # Validate document limits
            await self.__validate_document_limits(document_data.conversation_id, document_data.files)
            files = document_data.files
            message_id = document_data.message_id
            signalr_user_id = document_data.conversation_id

            results = []
            for file in files:
                # Upload file directly using message_id
                file_url = await self.__upload(file, message_id)
                document = await self.document_db_repository.create(
                    message_public_id=message_id,
                    file_name=file.filename,  # type: ignore
                    file_size=file.size or 0,
                    file_type=file.content_type,  # type: ignore
                    file_url=file_url,
                )
                results.append(DocumentResponse.model_validate(document, from_attributes=True))

                await self.document_queue_repository.send_document_message(
                    blob_url=file_url, signalr_user_id=signalr_user_id
                )

            return results

        except (EntityNotFoundError, MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded) as e:
            logger.error('Business logic error: %s', e)
            raise
        except Exception as e:
            logger.error('Error creating documents: %s', e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all documents associated with a conversation.

        Args:
            conversation_id: UUID of the conversation to delete documents from
        """
        try:
            file_info = await self.document_db_repository.get_file_info_for_deletion(conversation_id)

            blob_paths = []
            for file_name, message_id in file_info:
                blob_path = self.__generate_blob_path(file_name, message_id)
                blob_paths.append(blob_path)

            if blob_paths:
                await self.document_blob_repository.delete_many(blob_paths)

            await self.document_db_repository.delete_many(conversation_id)

            logger.info('Successfully deleted all documents for conversation ID "%s"', conversation_id)
        except Exception as e:
            logger.error('Failed to delete documents for conversation ID %s: "%s"', conversation_id, e)
            raise

    async def __validate_document_limits(self, conversation_id: UUID, files: list[UploadFile]) -> None:
        """Validate document count and size limits."""
        doc_count = await self.document_db_repository.count_documents_by_conversation_id(conversation_id)
        new_doc_count = len(files)
        if doc_count + new_doc_count > settings.document_storage.max_docs_per_conversation:
            raise MaximumDocumentsNumberExceeded()

        total_size = await self.document_db_repository.get_total_size_by_conversation_id(conversation_id)
        new_size = sum(file.size or 0 for file in files)
        if total_size + new_size > settings.document_storage.max_conversation_size:
            raise MaximumDocumentsSizeExceeded(projected_size=total_size + new_size)

    async def __upload(self, file: UploadFile, message_id: UUID) -> str:
        """
        Upload a file to blob storage.

        Args:
            file: File to upload (already validated by schema)
            message_id: The message ID

        Returns:
            URL of the uploaded blob

        Raises:
            Exception: If upload fails
        """
        file_name = cast(str, file.filename)
        blob_path = self.__generate_blob_path(file_name, message_id)

        try:
            content = await file.read()

            logger.info(
                "Uploading file '%s' (size: %d bytes, type: %s)",
                blob_path,
                file.size or 0,
                file.content_type,
            )

            return await self.document_blob_repository.upload_file(
                file_name=blob_path,
                content=content,
                content_type=file.content_type,
            )

        except Exception as e:
            logger.error("Failed to upload file '%s': %s", blob_path, e)
            raise

    def __generate_blob_path(self, file_name: str, message_id: UUID) -> str:
        """
        Generate a path for storing a file in blob storage.

        Args:
            file: File to upload
            message_id: The message ID

        Returns:
            Formatted blob path
        """
        return f'uploads/{message_id}/{file_name}'
