from contextlib import contextmanager
import json
import logging
from typing import Any, Dict, Iterator, List
import urllib.parse

from app.durable_functions.application.config import settings
from azure.core.exceptions import AzureError, ResourceExistsError, ResourceNotFoundError
from azure.storage.blob import BlobServiceClient, ContainerClient, ContentSettings


logger = logging.getLogger(__name__)


class BlobStorageError(Exception):
    """Base exception for blob storage operations."""

    pass


class BlobUploadError(BlobStorageError):
    """Exception raised when a blob upload fails."""

    pass


class BlobDownloadError(BlobStorageError):
    """Exception raised when a blob download fails."""

    pass


class BlobStorageHelper:
    """
    Helper class for Azure Blob Storage operations.

    This class provides methods for common blob storage operations such as
    uploading and downloading files, working with JSON data, and handling
    blob URLs.
    """

    def __init__(self, connection_string: str | None = None, container_name: str | None = None):
        """
        Initialize the blob storage helper.

        Args:
            connection_string: Azure Storage connection string. If None, uses the value from settings.
            container_name: Blob container name. If None, uses the value from settings.
        """
        self.connection_string = connection_string or settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING
        self.container_name = container_name or settings.BLOB_STORAGE_SETTINGS.CONTAINER_NAME
        self._service_client = None
        self._container_initialized = False

    def __enter__(self) -> 'BlobStorageHelper':
        """Enable context manager support."""
        return self

    def __exit__(self, *_) -> None:
        """Clean up resources when exiting context."""
        if self._service_client:
            self._service_client.close()
            self._service_client = None

    @property
    def service_client(self) -> BlobServiceClient:
        """
        Get the blob service client, creating it if it doesn't exist.

        Returns:
            BlobServiceClient instance
        """
        if not self._service_client:
            self._service_client = BlobServiceClient.from_connection_string(self.connection_string)
        return self._service_client

    @contextmanager
    def get_container_client(self) -> Iterator[ContainerClient]:
        """
        Get a container client as a context manager.

        Yields:
            ContainerClient for the configured container
        """
        container_client = self.service_client.get_container_client(self.container_name)
        yield container_client

    # Container operations

    def ensure_container_exists(self) -> None:
        """
        Ensure the container exists, creating it if necessary.

        This method is idempotent and can be called multiple times safely.
        """
        if self._container_initialized:
            return

        try:
            self.service_client.create_container(self.container_name)
            logger.info("Container '%s' created", self.container_name)
        except ResourceExistsError:
            logger.debug("Container '%s' already exists", self.container_name)

        self._container_initialized = True

    def blob_exists(self, blob_path: str) -> bool:
        """
        Check if a blob exists in the container.

        Args:
            blob_path: Path within the container

        Returns:
            True if the blob exists, False otherwise
        """
        self.ensure_container_exists()

        try:
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=blob_path)
            return blob_client.exists()
        except Exception as e:
            logger.error("Error checking if blob '%s' exists: %s", blob_path, e)
            return False

    def list_blobs(self, prefix: str | None = None) -> List[str]:
        """
        List blobs in the container with an optional prefix.

        Args:
            prefix: Optional prefix to filter blobs

        Returns:
            List of blob names
        """
        self.ensure_container_exists()

        try:
            with self.get_container_client() as container_client:
                return [blob.name for blob in container_client.list_blobs(name_starts_with=prefix)]
        except Exception as e:
            logger.error("Error listing blobs with prefix '%s': %s", prefix, e)
            return []

    # Upload operations

    def upload_file(self, blob_path: str, content: bytes, content_type: str | None = None) -> str:
        """
        Upload a file to blob storage.

        Args:
            blob_path: Path within the container
            content: File content as bytes
            content_type: MIME type of the file

        Returns:
            URL of the uploaded blob

        Raises:
            BlobUploadError: If the upload fails
        """
        if not blob_path:
            raise ValueError('Blob path cannot be empty')

        # Normalize the blob path to use forward slashes
        blob_path = blob_path.replace('\\', '/')

        self.ensure_container_exists()

        try:
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=blob_path)

            content_settings = ContentSettings(content_type=content_type) if content_type else None

            blob_client.upload_blob(
                content,
                content_settings=content_settings,
                overwrite=True,
            )

            logger.info("File '%s' uploaded successfully", blob_path)
            return blob_client.url

        except ValueError as e:
            logger.error("Invalid input for blob '%s': %s", blob_path, e)
            raise BlobUploadError(f'Invalid input: {str(e)}') from e
        except AzureError as e:
            logger.error("Azure error uploading blob '%s': %s", blob_path, e)
            raise BlobUploadError(f'Azure error: {str(e)}') from e
        except Exception as e:
            logger.error("Failed to upload file '%s': %s", blob_path, e)
            raise BlobUploadError(f'Unexpected error: {str(e)}') from e

    def upload_json(self, blob_path: str, data: Dict[str, Any]) -> str:
        """
        Upload JSON data to blob storage.

        Args:
            blob_path: Path within the container
            data: Dictionary to be serialized as JSON

        Returns:
            URL of the uploaded blob

        Raises:
            BlobUploadError: If the upload fails
        """
        try:
            content = json.dumps(data, ensure_ascii=False).encode('utf-8')
            return self.upload_file(blob_path, content, 'application/json')
        except (TypeError, ValueError) as e:
            logger.error("JSON serialization error for '%s': %s", blob_path, e)
            raise BlobUploadError(f'JSON serialization error: {str(e)}') from e

    # Download operations

    def download_file(self, blob_path: str) -> bytes:
        """
        Download a file from blob storage.

        Args:
            blob_path: Path within the container

        Returns:
            File content as bytes

        Raises:
            BlobDownloadError: If the download fails
        """
        if not blob_path:
            raise ValueError('Blob path cannot be empty')

        # Normalize the blob path to use forward slashes
        blob_path = blob_path.replace('\\', '/')

        try:
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=blob_path)

            if not blob_client.exists():
                raise ResourceNotFoundError(f"Blob '{blob_path}' does not exist")

            return blob_client.download_blob().readall()

        except ResourceNotFoundError as e:
            logger.error("Blob '%s' not found: %s", blob_path, e)
            raise BlobDownloadError(f'Blob not found: {blob_path}') from e
        except AzureError as e:
            logger.error("Azure error downloading blob '%s': %s", blob_path, e)
            raise BlobDownloadError(f'Azure error: {str(e)}') from e
        except Exception as e:
            logger.error("Failed to download file '%s': %s", blob_path, e)
            raise BlobDownloadError(f'Unexpected error: {str(e)}') from e

    def download_json(self, blob_path: str) -> Dict[str, Any]:
        """
        Download and parse JSON data from blob storage.

        Args:
            blob_path: Path within the container

        Returns:
            Parsed JSON data as dictionary

        Raises:
            BlobDownloadError: If the download fails or JSON parsing fails
        """
        try:
            content = self.download_file(blob_path)
            return json.loads(content)
        except json.JSONDecodeError as e:
            logger.error("JSON parsing error for '%s': %s", blob_path, e)
            raise BlobDownloadError(f'JSON parsing error: {str(e)}') from e

    def get_blob_from_url(self, blob_url: str) -> bytes:
        """
        Download blob content from a URL.

        Args:
            blob_url: Full URL to the blob

        Returns:
            Blob content as bytes

        Raises:
            BlobDownloadError: If the download fails
        """
        if not blob_url:
            raise ValueError('Blob URL cannot be empty')

        try:
            path = self.get_blob_path_from_url(blob_url)
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=path)

            if not blob_client.exists():
                raise ResourceNotFoundError(f"Blob at URL '{blob_url}' does not exist")

            return blob_client.download_blob().readall()
        except ResourceNotFoundError as e:
            logger.error("Blob at URL '%s' not found: %s", blob_url, e)
            raise BlobDownloadError(f'Blob not found: {blob_url}') from e
        except AzureError as e:
            logger.error("Azure error downloading blob from URL '%s': %s", blob_url, e)
            raise BlobDownloadError(f'Azure error: {str(e)}') from e
        except Exception as e:
            logger.error("Failed to download blob from URL '%s': %s", blob_url, e)
            raise BlobDownloadError(f'Unexpected error: {str(e)}') from e

    def get_blob_path_from_url(self, blob_url: str) -> str:
        """
        Extract the blob path from a URL, keeping only the last three segments (uploads/{message_id}/{filename}).

        Args:
            blob_url: Full URL to the blob

        Returns:
            Blob path within the container
        """
        if not blob_url:
            raise ValueError('Blob URL cannot be empty')

        parsed = urllib.parse.urlparse(blob_url)
        path = parsed.path.lstrip('/')
        parts = path.split('/')

        # Skip container name if present and keep the last three parts
        if len(parts) >= 3:
            # If the container name is in the path, skip it
            if len(parts) > 0 and parts[0] == self.container_name and len(parts) >= 4:
                return '/'.join(parts[-3:])
            return '/'.join(parts[-3:])

        return path
