import logging
import os
from typing import Any, Dict

import azure.durable_functions as df
from app.durable_functions.utils import (
    ActivityName,
    BlobStorageHelper,
    DocumentIntelligenceHelper,
    ExctractStatus,
    SignalRApiClient,
)

from .models import (
    ExtractDocumentTextActivityInput,
    ExtractDocumentTextActivityOutput,
    ExtractDocumentTextActivityOutputFailed,
    SendNotificationActivityInput,
    UpdateProcessingStatusActivityInput,
)


logger = logging.getLogger(__name__)

bp = df.Blueprint()


@bp.activity_trigger('document', ActivityName.ExtractDocumentText)
def extract_document_text(
    document: ExtractDocumentTextActivityInput,
) -> ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed:
    """
    Activity function to extract text from a document using Document Intelligence.
    """
    try:
        file_name = document.file_name
        logger.info(f'Extracting text from document {file_name} for message {document.message_id}')

        blob_helper = BlobStorageHelper()
        document_bytes = blob_helper.get_blob_from_url(document.blob_url)

        try:
            doc_intelligence = DocumentIntelligenceHelper()
            extraction_result = doc_intelligence.extract_text_from_document(document_bytes)
        except Exception as e:
            logger.error(f'Document Intelligence extraction failed: {str(e)}')
            return ExtractDocumentTextActivityOutputFailed(
                message_id=document.message_id,
                file_name=file_name,
                error=f'Document extraction failed: {str(e)}',
                status=ExctractStatus.Failed,
            )

        base_name = os.path.splitext(file_name)[0]  # type: ignore
        extract_filename = f'{base_name}.json'
        extraction_path = f'extracted/{document.message_id}/{extract_filename}'
        extraction_url = blob_helper.upload_json(extraction_path, extraction_result)

        return ExtractDocumentTextActivityOutput(
            message_id=document.message_id,
            file_name=file_name,
            extraction_url=extraction_url,
            text_content=extraction_result['text'],
            metadata={
                **extraction_result['metadata'],
                'original_blob_url': document.blob_url,
            },
            status=ExctractStatus.Success,
        )

    except Exception as e:
        logger.error(f'Error extracting document text: {str(e)}')
        raise


@bp.activity_trigger('updates', ActivityName.UpdateProcessingStatus)
async def update_processing_status(updates: UpdateProcessingStatusActivityInput) -> Dict[str, Any]:
    """
    Activity function to update the file processing status.
    """
    try:
        logger.info(f'Updating processing status for message_id {updates.message_id}: {updates.status}')

        # For now, we'll just log the status update since the endpoint is temporary
        # In the future, this could update the message status in the database directly
        logger.info(f'Processing status update: {updates.status} - {updates.message}')
        if updates.metadata:
            logger.info(f'Metadata: {updates.metadata}')

        return {
            'status': 'success',
            'message_id': updates.message_id,
            'processing_status': updates.status,
            'message': updates.message,
            'metadata': updates.metadata
        }

    except Exception as e:
        logger.error(f'Error updating processing status: {str(e)}')
        raise


@bp.activity_trigger('notification', ActivityName.SendNotification)
async def send_notification(notification: SendNotificationActivityInput) -> None:
    """
    Activity function to send a notification via SignalR.

    Args:
        notification: Notification data including event type, data, and optional signalr_user_id

    Returns:
        None
    """
    try:
        logger.info(f'Sending notification event: {notification.event_type}')
        signalr_client = SignalRApiClient()

        # Pass the signalr_user_id to the send_notification method
        await signalr_client.send_notification(
            event_type=notification.event_type, data=notification.data, user_id=notification.signalr_user_id
        )

    except Exception as e:
        logger.error(f'Error sending notification: {str(e)}')
        raise
