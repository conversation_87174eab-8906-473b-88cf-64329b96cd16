from typing import Any

from utils import DFBaseModel, EventType, ExctractStatus


__all__ = [
    'SendNotificationActivityInput',
    'UpdateProcessingStatusActivityInput',
    'ExtractDocumentTextActivityInput',
    'ChunkDocumentActivityInput',
    'ChunkDocumentActivityOutput',
]


class SendNotificationActivityInput(DFBaseModel):
    event_type: EventType
    data: dict[str, Any]
    signalr_user_id: str | None = None


class UpdateProcessingStatusActivityInput(DFBaseModel):
    message_id: str
    status: str
    message: str
    metadata: dict[str, Any] = {}


class ExtractDocumentTextActivityInput(DFBaseModel):
    blob_url: str
    message_id: str
    file_name: str


class BaseExtractDocumentTextActivityOutput(DFBaseModel):
    message_id: str
    file_name: str
    status: ExctractStatus


class ExtractDocumentTextActivityOutput(BaseExtractDocumentTextActivityOutput):
    extraction_url: str
    text_content: str
    metadata: dict[str, Any]


class ExtractDocumentTextActivityOutputFailed(BaseExtractDocumentTextActivityOutput):
    error: str


class ChunkDocumentActivityInput(DFBaseModel):
    message_id: str
    file_name: str
    extraction_url: str
    text_content: str
    metadata: dict[str, Any]


class ChunkDocumentActivityOutput(DFBaseModel):
    message_id: str
    file_name: str
    chunk_count: int
    chunk_urls: list[dict[str, Any]]
