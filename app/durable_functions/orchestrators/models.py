from typing import Any

from app.durable_functions.utils import DFBaseModel


class ProcessDocumentInput(DFBaseModel):
    blob_url: str
    signalr_user_id: str | None = None


class BaseProcessingOrchestratorOutput(DFBaseModel):
    message_id: str
    file_name: str
    status: str


class DocumentProcessingOrchestratorOutput(BaseProcessingOrchestratorOutput):
    extraction_url: str | None = None
    chunk_count: int | None = None
    chunk_urls: list[dict[str, Any]] | None = None


class DocumentProcessingOrchestratorOutputFailed(BaseProcessingOrchestratorOutput):
    error: str
