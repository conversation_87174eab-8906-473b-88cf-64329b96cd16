import logging
import re

from activities.models import (
    ExtractDocumentTextActivityInput,
    ExtractDocumentTextActivityOutput,
    ExtractDocumentTextActivityOutputFailed,
    SendNotificationActivityInput,
    UpdateProcessingStatusActivityInput,
)
import azure.durable_functions as df
from utils import ActivityName, EventType, ExctractStatus, OrchestratorName, ProcessingStatus

from .models import (
    DocumentProcessingOrchestratorOutput,
    DocumentProcessingOrchestratorOutputFailed,
    ProcessDocumentInput,
)


logger = logging.getLogger(__name__)
bp = df.Blueprint()


def parse_blob_url(blob_url: str):
    # Example: .../uploads/<message_id>/<file_name>
    match = re.search(r'/uploads/([a-f0-9\-]+)/([^/]+)$', blob_url)
    if not match:
        raise ValueError('Invalid blob_url format')
    message_id, file_name = match.groups()
    return message_id, file_name


@bp.orchestration_trigger('context', OrchestratorName.DocumentProcessing)
def document_processing_orchestrator(context: df.DurableOrchestrationContext):
    """
    Orchestrator function for document processing.

    This orchestrator coordinates the document processing workflow:
    1. Extract text from the document using Document Intelligence
    2. Save the extraction result to blob storage
    3. Chunk the document
    4. Save the chunks to blob storage
    5. Update the processing status

    Args:
        context: Durable orchestration context

    Returns:
        Dictionary containing the processing results
    """

    message_id, file_name, signalr_user_id = None, None, None
    try:
        input_dict = context.get_input()
        input_data = ProcessDocumentInput.model_validate(input_dict)

        blob_url = input_data.blob_url
        signalr_user_id = input_data.signalr_user_id
        message_id, file_name = parse_blob_url(blob_url)

        logger.info(f'Starting document processing for {file_name} with message ID {message_id}')

        # Update status to "processing"
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.DocumentExtractionStarted,
                message=f'Processing document: {file_name}',
            ),
        )

        # Send notification
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.DocumentExtractionStarted,
                data={'message_id': message_id, 'file_name': file_name},
                signalr_user_id=signalr_user_id,
            ),
        )

        retry_options = df.RetryOptions(first_retry_interval_in_milliseconds=5000, max_number_of_attempts=3)
        # Step 1: Extract text from the document with retries
        extraction_result: (
            ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed
        ) = yield context.call_activity_with_retry(
            ActivityName.ExtractDocumentText,
            retry_options,
            ExtractDocumentTextActivityInput(
                blob_url=blob_url,
                message_id=message_id,
                file_name=file_name,
            ),
        )

        # Check if extraction failed
        if extraction_result.status == ExctractStatus.Failed:
            # We know it's a failed result, so we can safely cast it
            failed_result = (
                extraction_result if isinstance(extraction_result, ExtractDocumentTextActivityOutputFailed) else None
            )
            error_message = failed_result.error if failed_result else 'Unknown extraction error'

            # Update status to "failed"
            yield context.call_activity(
                ActivityName.UpdateProcessingStatus,
                UpdateProcessingStatusActivityInput(
                    message_id=message_id,
                    status=ProcessingStatus.DocumentExtractionFailed,
                    message=f'Document processing failed: {error_message}',
                ),
            )

            # Send notification
            yield context.call_activity(
                ActivityName.SendNotification,
                SendNotificationActivityInput(
                    event_type=EventType.DocumentExtractionFailed,
                    data={'message_id': message_id, 'file_name': file_name, 'error': error_message},
                    signalr_user_id=signalr_user_id,
                ),
            )
            response = DocumentProcessingOrchestratorOutputFailed.model_validate(
                {
                    'message_id': message_id,
                    'file_name': file_name,
                    'status': ProcessingStatus.OrchestratorDocumentProcessingFailed,
                    'error': error_message,
                }
            ).model_dump()
            return response

        # At this point, we know extraction was successful, so we can safely cast
        success_result = extraction_result if isinstance(extraction_result, ExtractDocumentTextActivityOutput) else None

        if not success_result:
            # This should never happen based on our logic, but handle it just in case
            raise ValueError('Extraction result has success status but is not of success type')

        # Update status after extraction
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.DocumentExtractionCompleted,
                message=f'Text extracted from document: {file_name}',
                metadata={'extraction_url': success_result.extraction_url},
            ),
        )

        # Step 2: Chunk the document
        response = DocumentProcessingOrchestratorOutput.model_validate(
            {
                'message_id': message_id,
                'file_name': file_name,
                'status': ProcessingStatus.OrchestratorDocumentProcessingCompleted,
                'extraction_url': success_result.extraction_url,
                'chunk_count': 1,
                'chunk_urls': [],
            }
        ).model_dump()
        # azure function doesnt like returning pydantic models with model_dump() directly
        return response

    except Exception as e:
        logger.error('Error in document processing orchestrator: %s', str(e))

        # Handle the case where message_id might be None
        safe_message_id = message_id if message_id else 'unknown'
        safe_file_name = file_name if file_name else 'unknown'

        # Only try to send status updates if we have a message ID
        if message_id:
            try:
                yield context.call_activity(
                    ActivityName.UpdateProcessingStatus,
                    UpdateProcessingStatusActivityInput(
                        message_id=safe_message_id,
                        status=ProcessingStatus.DocumentProcessingFailed,
                        message=f'Document processing failed: {str(e)}',
                    ),
                )

                yield context.call_activity(
                    ActivityName.SendNotification,
                    SendNotificationActivityInput(
                        event_type=EventType.DocumentProcessingFailed,
                        data={'message_id': safe_message_id, 'file_name': safe_file_name, 'error': str(e)},
                        signalr_user_id=signalr_user_id,
                    ),
                )
            except Exception as inner_e:
                # Log but don't re-raise to ensure we still raise the original exception
                logger.error('Error sending failure notifications: %s', str(inner_e))

        # Re-raise the original exception
        raise
