# Document Processing Azure Durable Functions

This module contains Azure Durable Functions for document processing in the KX GenAI Qual application.

## Overview

The document processing workflow consists of the following steps:

1. **File Upload**: The API uploads files to Azure Blob Storage at `/uploads/{message_id}/{filename}`
2. **Queue Trigger**: A message is added to a queue to trigger document processing
3. **Text Extraction**: Document Intelligence extracts text from the document
4. **Extraction Storage**: The extraction result is saved to `/extracted/{message_id}/{filename}`
5. **Chunking**: The extracted text is chunked using a combined strategy (semantic + sentence-based)
6. **Chunk Storage**: Chunks are saved to `/chunks/{message_id}/{original_filename}_chunk_{chunk_index}.json`
7. **Status Updates**: The API is notified of processing status changes
8. **SignalR Notifications**: The frontend is notified of status changes via SignalR

## Folder Structure

- `activities/`: Activity functions for each step of the workflow
- `orchestrators/`: Orchestrator functions to coordinate the workflow
- `triggers/`: HTTP and queue triggers to start the workflow
- `utils/`: Utility modules for blob storage, document intelligence, chunking, and API integration
- `application/`: Configuration and shared application code

## Activities

The following activity functions are available:

- `extract_document_text`: Extracts text from a document using Document Intelligence.
- `chunk_document`: Chunks the extracted text content of a document.
- `update_processing_status`: Updates the processing status of a document via the API.
- `send_notification`: Sends a notification via SignalR.

## Orchestrators

The following orchestrators are available:

- `document_processing_orchestrator`: Coordinates the document processing workflow, including text extraction, chunking, and status updates.

## HTTP Triggers

The following HTTP triggers are available:

- `check-status/{instance_id}`: Checks the status of a Durable Functions orchestration instance.
- `process-document`: HTTP endpoint to start document processing.

## Queue Triggers

The following queue triggers are available:

- `process_document_queue`: Triggered by messages in the `document-processing` queue to start the document processing orchestration.

## Configuration

The application uses environment-specific configuration files with Pydantic settings management. Configuration is loaded from environment-specific `.env` files (e.g., `local.env`, `dev.env`).

### Required Settings

#### Azure Storage
- `BLOB_STORAGE_SETTINGS__CONNECTION_STRING`: Connection string for the Azure Storage account
- `BLOB_STORAGE_SETTINGS__CONTAINER_NAME`: Blob container name (default: `documents`)
- `QUEUE_SETTINGS__CONNECTION_STRING`: Connection string for the Azure Queue Storage account
- `QUEUE_SETTINGS__QUEUE_NAME`: Queue name for document processing (default: `document-processing`)

#### Azure Functions
- `AzureWebJobsStorage`: Azure Storage connection string for Functions runtime
- `FUNCTIONS_WORKER_RUNTIME`: The runtime for the Azure Functions worker (e.g., python)

#### Document Intelligence
- `DOCUMENT_INTELLIGENCE_SETTINGS__ENDPOINT`: Document Intelligence endpoint
- `DOCUMENT_INTELLIGENCE_SETTINGS__KEY`: Document Intelligence API key
- `DOCUMENT_INTELLIGENCE_SETTINGS__MODEL_NAME`: Model name (default: `prebuilt-layout`)

#### API Integration
- `API_BASE_URL`: Base URL of the API (default: `http://localhost:8000`)
- `ENVIRONMENT`: The application environment (e.g., local, development, production)

#### SignalR
- `SIGNALR_SETTINGS__CONNECTION_STRING`: Connection string for Azure SignalR service
- `SIGNALR_SETTINGS__HUB_NAME`: Name of the SignalR hub (default: `qualMessageHub`)

#### Chunking Configuration
- `CHUNKING_SETTINGS__CHUNK_SIZE`: Size of each chunk in tokens (default: 512)
- `CHUNKING_SETTINGS__CHUNK_OVERLAP`: Overlap between chunks in tokens (default: 50)
- `CHUNKING_SETTINGS__ENCODING_NAME`: Encoding to use for tokenization (default: `cl100k_base`)

## Chunking Strategies

The module uses LangChain's text splitters for document chunking with the following configuration:

- **Default Strategy**: LangChain's `RecursiveCharacterTextSplitter` with tiktoken
- **Chunk Size**: 512 tokens (configurable)
- **Chunk Overlap**: 50 tokens (configurable)
- **Default Encoding**: cl100k_base
- **Default Separators**: ['\n\n', '\n', '. ', ' ', '']

The chunking process:
1. Splits the document text into chunks using the configured strategy
2. Assigns a unique ID to each chunk
3. Adds metadata including the original document information
4. Calculates token count for each chunk
5. Saves chunks to blob storage with path `/chunks/{message_id}/{filename}_chunk_{index}.json`

## API Integration

The functions integrate with the API in two ways:

1. **Status Updates**: The API is notified of processing status changes via PUT requests to `/api/messages/{id}/data_processing`
2. **SignalR Notifications**: The frontend is notified of status changes via SignalR

## Local Development

1. Install the Azure Functions Core Tools
2. Install the required packages: `pip install -r requirements.txt`
3. Create a `local.env` file with your configuration (see sample below)
4. Run the functions locally using the helper script: `python run_local.py`

### Sample local.env file

```
DEBUG=true
ENVIRONMENT=local

# Azure Storage settings
BLOB_STORAGE_SETTINGS__CONNECTION_STRING=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;
BLOB_STORAGE_SETTINGS__CONTAINER_NAME=documents

# Queue settings
QUEUE_SETTINGS__CONNECTION_STRING=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;
QUEUE_SETTINGS__QUEUE_NAME=document-processing

# Document Intelligence settings
DOCUMENT_INTELLIGENCE_SETTINGS__ENDPOINT=your-document-intelligence-endpoint
DOCUMENT_INTELLIGENCE_SETTINGS__KEY=your-document-intelligence-key
DOCUMENT_INTELLIGENCE_SETTINGS__MODEL_NAME=prebuilt-layout

# SignalR settings
SIGNALR_SETTINGS__CONNECTION_STRING=your-signalr-connection-string
SIGNALR_SETTINGS__HUB_NAME=qualMessageHub

# API settings
API_BASE_URL=http://localhost:8000
```

### Using Azurite for Local Development

For local development, you can use Azurite to emulate Azure Storage:

1. Start Azurite using Docker: `docker-compose up -d azurite`
2. Azurite will provide:
   - Blob service on port 10000
   - Queue service on port 10001
   - Table service on port 10002
3. Use the connection strings shown in the sample `local.env` above

## Deployment

Deploy to Azure Functions using the Azure Functions Core Tools or Azure DevOps pipelines:

### Using Azure Functions Core Tools

```bash
func azure functionapp publish <function-app-name>
```

### Using Azure DevOps

Configure a pipeline that:
1. Installs dependencies
2. Runs tests
3. Publishes the function app to Azure

## Testing

To test the functions locally:

1. Start Azurite for local storage emulation
2. Run the functions using `python run_local.py`
3. Use the HTTP trigger to start document processing:
   ```bash
   curl -X POST http://localhost:7071/api/process-document \
     -H "Content-Type: application/json" \
     -d '{"blob_url": "http://127.0.0.1:10000/devstoreaccount1/documents/uploads/message-id/document.pdf"}'
   ```
4. Check the status using the check-status endpoint:
   ```bash
   curl http://localhost:7071/api/check-status/{instance_id}
   ```

Alternatively, you can add a test document to the queue directly to trigger processing.
