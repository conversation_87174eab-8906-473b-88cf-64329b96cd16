import json
import logging

from application.config import settings
import azure.durable_functions as df
from azure.functions import QueueMessage
from utils import OrchestratorName


logger = logging.getLogger(__name__)

QUEUE_NAME = settings.QUEUE_SETTINGS.DOCUMENT_PROCESSING_QUEUE
CONNECTION_ENV = settings.QUEUE_SETTINGS.CONNECTION_ENV

bp = df.Blueprint()


@bp.queue_trigger(arg_name='msg', queue_name=QUEUE_NAME, connection=CONNECTION_ENV)
@bp.durable_client_input(client_name='client')
async def process_document_queue(msg: QueueMessage, client: df.DurableOrchestrationClient) -> None:
    """
    Queue trigger to start document processing.

    Expected message format:
    {
        "blob_url": "https://...",
        "signalr_user_id": "user-uuid" (optional)
    }

    Args:
        msg: Queue message
        client: Durable orchestration client

    Returns:
        None
    """
    try:
        msg_body = msg.get_body().decode('utf-8')
        logger.info(f'Received queue message: {msg_body}')
        msg_json = json.loads(msg_body)

        blob_url = msg_json.get('blob_url')
        if not blob_url:
            logger.error("Missing 'blob_url' in queue message.")
            return

        # Extract the signalr_user_id if present
        signalr_user_id = msg_json.get('signalr_user_id')

        # Prepare input for the orchestrator
        orchestrator_input = {'blob_url': blob_url}
        if signalr_user_id:
            orchestrator_input['signalr_user_id'] = signalr_user_id

        orchestrator_name = OrchestratorName.DocumentProcessing
        logger.info(f'Starting orchestrator: {orchestrator_name} with input: {orchestrator_input}')
        instance_id = await client.start_new(orchestrator_name, None, orchestrator_input)
        logger.info(f'Started orchestration instance: {instance_id}')

    except Exception as e:
        logger.exception(f'Error processing queue message: {e}')
