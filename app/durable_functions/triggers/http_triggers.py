from http import HTTPStatus
import json
import logging

import azure.durable_functions as df
import azure.functions as func
from app.durable_functions.utils import (
    OrchestratorName,
    SignalRApiClient,
)


logger = logging.getLogger(__name__)

bp = df.Blueprint()


@bp.route(route='process-document', methods=['POST'])
@bp.durable_client_input(client_name='client')
async def process_document_http(req: func.HttpRequest, client: df.DurableOrchestrationClient) -> func.HttpResponse:
    """
    HTTP trigger to start document processing.
    """
    try:
        req_body = req.get_json()

        if 'blob_url' not in req_body:
            return func.HttpResponse(
                body=json.dumps({'error': 'Missing required field: blob_url'}),
                status_code=400,
                mimetype='application/json',
            )

        instance_id = await client.start_new(OrchestratorName.DocumentProcessing, None, req_body)
        response = {
            'id': instance_id,
            'statusQueryGetUri': client.create_check_status_response(req, instance_id).get_body().decode('utf-8'),
            'message': f'Document processing started for {req_body.get("blob_url")}',
        }
        return func.HttpResponse(
            body=json.dumps(response), status_code=HTTPStatus.ACCEPTED, mimetype='application/json'
        )

    except ValueError as e:
        return func.HttpResponse(
            body=json.dumps({'error': f'Invalid JSON: {str(e)}'}),
            status_code=HTTPStatus.BAD_REQUEST,
            mimetype='application/json',
        )
    except Exception as e:
        logger.error(f'Error starting document processing: {str(e)}')
        return func.HttpResponse(
            body=json.dumps({'error': f'Internal server error: {str(e)}'}),
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype='application/json',
        )


@bp.route(route='negotiate', methods=['POST'])
async def negotiate(req: func.HttpRequest) -> func.HttpResponse:
    """
    SignalR negotiation endpoint (POST version).
    Accepts userId from query string or JSON body.
    """
    try:
        user_id = req.params.get('userId')
        if not user_id:
            try:
                body = req.get_json()
                user_id = body.get('userId')
            except ValueError:
                pass

        if not user_id:
            return func.HttpResponse(
                body=json.dumps({'error': "Missing 'userId' in query or body."}),
                status_code=HTTPStatus.BAD_REQUEST,
                mimetype='application/json',
            )

        signalr_client = SignalRApiClient()
        connection_attributes = signalr_client.get_user_connection_attributes(user_id)
        return func.HttpResponse(
            body=json.dumps(connection_attributes.model_dump()), status_code=HTTPStatus.OK, mimetype='application/json'
        )

    except Exception as e:
        logger.error(f'Negotiation error: {str(e)}')
        return func.HttpResponse(
            body=json.dumps({'error': str(e)}),
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype='application/json',
        )


@bp.route(route='check-status/{instance_id}')
@bp.durable_client_input(client_name='client')
async def check_status(req: func.HttpRequest, client: df.DurableOrchestrationClient) -> func.HttpResponse:
    """HTTP trigger to check status of an orchestration."""
    try:
        instance_id = req.route_params.get('instance_id')
        if not instance_id:
            return func.HttpResponse(body='Missing instance ID', status_code=HTTPStatus.BAD_REQUEST)
        status = await client.get_status(instance_id)
        return func.HttpResponse(
            body=json.dumps(status.to_json()), status_code=HTTPStatus.OK, mimetype='application/json'
        )
    except Exception as e:
        logger.error(f'Error checking status: {str(e)}')
        return func.HttpResponse(body=f'Error checking status: {str(e)}', status_code=HTTPStatus.INTERNAL_SERVER_ERROR)
