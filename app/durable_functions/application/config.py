import os

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


ENVIRONMENT: str = os.environ['ENVIRONMENT']


class SignalRSettings(BaseModel):
    CONNECTION_STRING: str
    BASE_URL: str
    ACCESS_KEY: str
    VERSION: str = 'v1'
    SERVER_NAME: str = 'QualMessageServer'
    HUB_NAME: str = 'qualMessageHub'
    TARGET: str = 'qualMessageHub'


class BlobStorageSettings(BaseModel):
    CONNECTION_STRING: str
    CONTAINER_NAME: str = 'documents'


class QueueSettings(BaseModel):
    CONNECTION_STRING: str
    DOCUMENT_PROCESSING_QUEUE: str = 'document-processing'
    CONNECTION_ENV: str = 'AZURE_QUEUE_CONNECTION_STRING'


class DocumentIntelligenceSettings(BaseModel):
    ENDPOINT: str
    KEY: str
    MODEL_NAME: str = 'prebuilt-layout'


class ChunkingSettings(BaseModel):
    CHUNK_SIZE: int = 512
    CHUNK_OVERLAP: int = 50
    ENCODING_NAME: str = 'cl100k_base'
    SEPARATORS: list[str] = ['\n\n', '\n', '. ', ' ', '']


class Settings(BaseSettings):
    DEBUG: bool = True
    SIGNALR_SETTINGS: SignalRSettings
    BLOB_STORAGE_SETTINGS: BlobStorageSettings
    QUEUE_SETTINGS: QueueSettings
    DOCUMENT_INTELLIGENCE_SETTINGS: DocumentIntelligenceSettings
    CHUNKING_SETTINGS: ChunkingSettings = Field(default=ChunkingSettings())

    class Config:
        env_file = f'{ENVIRONMENT}.env'
        env_nested_delimiter = '__'


settings = Settings()  # type: ignore
