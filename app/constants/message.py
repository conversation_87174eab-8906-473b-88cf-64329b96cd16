from pathlib import Path

from core.enum import StrEnum


__all__ = ['MessageRole', 'MessageType', 'OptionType', 'WELCOME_MESSAGE', 'DASH_TASK_SELECTED_TEMPLATE']


class MessageRole(StrEnum):
    SYSTEM = 'system'
    USER = 'user'


class MessageType(StrEnum):
    TEXT = 'text'
    FILE = 'file'
    FORM = 'form'
    TEXT_WITH_FILE = 'text_with_file'


def _load_message(file_name: str) -> str:
    path = Path(__file__).parent.parent / 'assets' / 'conversation_messages' / file_name
    return path.read_text(encoding='utf-8').strip()


WELCOME_MESSAGE = _load_message('welcome_message.txt')
DASH_TASK_SELECTED_TEMPLATE = _load_message('dash_task_selected.txt')


class OptionType(StrEnum):
    DATES = 'dates'
    LDMF_COUNTRY = 'ldmf_country'
    CLIENT_NAME = 'client_name'
    KX_DASH_TASK = 'kx_dash_task'
