from typing import Any

from config import settings

from .base import ApplicationError


__all__ = ['MaximumDocumentsNumberExceeded', 'MaximumDocumentsSizeExceeded']


class MaximumDocumentsNumberExceeded(ApplicationError):
    """Raised when docs count exceeds a number."""

    def __init__(self):
        self.max_doc_count = settings.document_storage.max_docs_per_conversation
        super().__init__(f'Maximum number of documents ({self.max_doc_count}) per conversation would be exceeded')


class MaximumDocumentsSizeExceeded(ApplicationError):
    """Raised when docs size exceeds a max total size."""

    def __init__(self, projected_size: Any):
        self.projected_size = projected_size
        self.max_docs_size = settings.document_storage.max_conversation_size
        super().__init__(
            f'Total document size ({projected_size} bytes) would exceed the maximum allowed '
            f'({self.max_docs_size} bytes) per conversation'
        )
