from typing import Any

from .base import ApplicationError


__all__ = ['EntityNotFoundError']


class EntityNotFoundError(ApplicationError):
    """Raised when an entity cannot be found by its identifier."""

    def __init__(self, entity_type: Any, entity_id: Any):
        self.entity_type = entity_type
        self.entity_id = entity_id
        super().__init__(f'{entity_type} with ID {entity_id} not found')
