from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from api import router
from config import configure_logging, settings, setup_custom_openapi
from constants.environment import Environment
from middleware import AzureADAuthorizerMiddleware, ExceptionToResponseMiddleware


configure_logging(settings)

# App
app = FastAPI(
    title=settings.project_name,
    version=settings.version,
    debug=settings.debug,
    docs_url='/docs' if settings.environment in (Environment.DEV, Environment.QA) else None,
    redoc_url=None,
    root_path='/api',
)

# Router
app.include_router(router)

# Middleware
app.add_middleware(AzureADAuthorizerMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_hosts,
    allow_credentials=True,
    allow_methods=('*',),
    allow_headers=('*',),
)
app.add_middleware(ExceptionToResponseMiddleware)  # NOTE: This middleware should always be added last!

# OpenAPI
setup_custom_openapi(app)
