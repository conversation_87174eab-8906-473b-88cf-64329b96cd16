from typing import Annotated, AsyncGenerator

from fastapi import Depends
from httpx import AsyncClient, Limits, Timeout

from config import settings


__all__ = ['HTTPClientDep']


async def get_http_client() -> AsyncGenerator[AsyncClient, None]:
    """
    Dependency that provides a new HTTP client for each request.
    Each request gets its own client that is properly cleaned up after use.
    """
    async with AsyncClient(
        timeout=Timeout(timeout=settings.http_client.timeout),
        follow_redirects=settings.http_client.follow_redirects,
        verify=settings.http_client.verify_ssl,
        limits=Limits(
            max_connections=settings.http_client.max_connections,
            max_keepalive_connections=settings.http_client.max_keepalive_connections,
        ),
    ) as client:
        yield client


HTTPClientDep = Annotated[AsyncClient, Depends(get_http_client)]
