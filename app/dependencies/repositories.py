from typing import Annotated

from fastapi import Depends

from config import settings
from repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentBlobRepository,
    DocumentDbRepository,
    DocumentQueueRepository,
    KXDashRepository,
)
from repositories.extracted_data import ExtractedDataRepository

from .db import DbSessionDep
from .http_client import HTTPClientDep


__all__ = [
    'ConversationRepositoryDep',
    'ConversationMessageRepositoryDep',
    'DocumentDbRepositoryDep',
    'DocumentBlobRepositoryDep',
    'KXDashRepositoryDep',
    'ExtractedDataRepositoryDep',
    'DocumentQueueRepositoryDep',
]


def get_conversation_repository(db_session: DbSessionDep) -> ConversationRepository:
    return ConversationRepository(db_session)


ConversationRepositoryDep = Annotated[ConversationRepository, Depends(get_conversation_repository)]


def get_conversation_message_repository(
    db_session: DbSessionDep, conversation_repository: ConversationRepositoryDep
) -> ConversationMessageRepository:
    return ConversationMessageRepository(db_session=db_session, conversation_repository=conversation_repository)


ConversationMessageRepositoryDep = Annotated[
    ConversationMessageRepository, Depends(get_conversation_message_repository)
]


def get_document_db_repository(db_session: DbSessionDep) -> DocumentDbRepository:
    return DocumentDbRepository(db_session)


DocumentDbRepositoryDep = Annotated[DocumentDbRepository, Depends(get_document_db_repository)]


async def get_document_blob_repository() -> DocumentBlobRepository:
    repo = DocumentBlobRepository(settings.document_storage.connection_string)
    await repo.initialize()
    return repo


DocumentBlobRepositoryDep = Annotated[DocumentBlobRepository, Depends(get_document_blob_repository)]


def get_kx_dash_repository(http_client: HTTPClientDep) -> KXDashRepository:
    return KXDashRepository(http_client)


KXDashRepositoryDep = Annotated[KXDashRepository, Depends(get_kx_dash_repository)]


def get_extracted_data_repository(
    db_session: DbSessionDep, conversation_repository: ConversationRepositoryDep
) -> ExtractedDataRepository:
    return ExtractedDataRepository(db_session=db_session, conversation_repository=conversation_repository)


ExtractedDataRepositoryDep = Annotated[ExtractedDataRepository, Depends(get_extracted_data_repository)]


async def get_document_queue_repository() -> DocumentQueueRepository:
    repo = DocumentQueueRepository(
        settings.document_queue.connection_string,
        settings.document_queue.document_queue_name,
    )
    await repo.initialize()
    return repo


DocumentQueueRepositoryDep = Annotated[DocumentQueueRepository, Depends(get_document_queue_repository)]
