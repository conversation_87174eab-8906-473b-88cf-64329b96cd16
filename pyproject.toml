[tool.pyright]
include = ["app", "tests", "scripts"]
extraPaths = ["app"]
exclude = [
    "**/__pycache__",
    "app/durable_functions/**/*",
]
pythonVersion = "3.12"
reportAttributeAccessIssue = "warning"
reportOptionalMemberAccess = "warning"
venvPath = "."
venv = ".venv"


[tool.ruff]
cache-dir = "~/.cache/ruff/kxqualsaiassistant"
exclude = [".venv"]
line-length = 120
src = ["app", "test", "scripts"]

[tool.ruff.format]
quote-style = "single"

[tool.ruff.lint]
extend-select = [
    "I",    # isort
    "T201", # print
    "T203", # pprint
]

[tool.ruff.lint.isort]
combine-as-imports = true
force-sort-within-sections = true
lines-after-imports = 2

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F403"]


[tool.pytest.ini_options]
addopts = "--verbose --cov=app --cov-report=term-missing --cov-report=html --cov-report=xml"
cache_dir = "~/.cache/pytest/kxqualsaiassistant"
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
testpaths = ["tests"]
asyncio_mode = "auto"
pythonpath = ["app"]
asyncio_default_test_loop_scope = "session"

[tool.coverage.run]
data_file = ".coverage/.coverage"
omit = [
    "app/migrations/**/*"
]

[tool.coverage.html]
directory = ".coverage/htmlcov"

[tool.coverage.xml]
output = ".coverage/coverage.xml"
