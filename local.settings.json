{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "AzureWebJobsFeatureFlags": "EnableWorkerIndexing", "ENVIRONMENT": "local", "FUNCTIONS_WORKER_RUNTIME": "python", "PYTHONPATH": ".:app", "DOCUMENT_INTELLIGENCE_ENDPOINT": "https://devtestgermany.cognitiveservices.azure.com/", "DOCUMENT_INTELLIGENCE_KEY": "609rgQmV2cB93fBJUfXE44eVKR2sMJLmoq2rvQBzb5VY4avg04WJJQQJ99BEACPV0roXJ3w3AAALACOGR7uB", "DOCUMENT_PROCESSING_QUEUE": "document-processing", "BLOB_CONTAINER_NAME": "documents", "API_BASE_URL": "http://localhost:8000", "SIGNAL_R_BASE_URL": "http://localhost:8000", "AZURE_STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1", "AZURE_QUEUE_CONNECTION_STRING": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;", "AzureSignalRConnectionString": "Endpoint=https://qualtestsignalr.service.signalr.net;AccessKey=AMnCNa0WUM7nloEtx6vyKn0BEeaGZa1Y9C9jlNGc8O79GysnO4MjJQQJ99BEACPV0roXJ3w3AAAAASRSnfTg;Version=1.0;"}, "ConnectionStrings": {}}