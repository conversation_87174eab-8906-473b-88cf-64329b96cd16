#!/usr/bin/env python3
"""
Test script to verify that the durable functions can import FastAPI app modules.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_durable_functions_imports():
    """Test that durable functions can import their modules correctly."""
    try:
        # Test importing durable functions modules
        from app.durable_functions.activities import document_processing_acttivity_bp
        from app.durable_functions.orchestrators import document_processing_bp
        from app.durable_functions.triggers import http_triggers_bp, queue_triggers_bp
        
        print("✅ Successfully imported durable functions modules")
        
        # Test importing FastAPI app modules
        from app.dependencies.db import async_session_local
        from app.repositories import ConversationRepository, ConversationMessageRepository
        from app.services import ConversationService, ConversationMessageService
        
        print("✅ Successfully imported FastAPI app modules")
        
        # Test importing durable functions utils
        from app.durable_functions.utils import (
            Activity<PERSON>ame,
            BlobS<PERSON>age<PERSON>elper,
            Document<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            SignalRApiClient,
        )
        
        print("✅ Successfully imported durable functions utils")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("Testing durable functions integration with FastAPI app...")
    success = test_durable_functions_imports()
    
    if success:
        print("\n🎉 All imports successful! Integration is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Integration test failed!")
        sys.exit(1)
