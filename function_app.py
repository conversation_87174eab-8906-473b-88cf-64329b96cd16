import logging

from app.durable_functions.activities import document_processing_acttivity_bp
import azure.durable_functions as df
import azure.functions as func
from app.durable_functions.orchestrators import document_processing_bp
from app.durable_functions.triggers import http_triggers_bp, queue_triggers_bp


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = df.DFApp(http_auth_level=func.AuthLevel.ANONYMOUS)

app.register_blueprint(document_processing_bp)
app.register_blueprint(http_triggers_bp)
app.register_blueprint(queue_triggers_bp)
app.register_blueprint(document_processing_acttivity_bp)
