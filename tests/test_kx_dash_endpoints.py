from unittest.mock import patch

from fastapi import status
from httpx import AsyncClient

from constants.operation_ids import operation_ids
from exceptions import EntityNotFoundError
from schemas import DashTaskResponse


@patch('services.kx_dash.KXDashService.list')
async def test_list_activities_success(
    mock_list, auth_mock, async_client: AsyncClient, url_resolver, mock_activities_response
):
    mock_list.return_value = mock_activities_response
    url = url_resolver.reverse(operation_ids.kx_dash.LIST)

    response = await async_client.get(url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_200_OK
    mock_list.assert_called_once()

    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 1

    activity = data[0]
    assert activity['activity_id'] == mock_activities_response[0].activity_id
    assert activity['activity_name'] == mock_activities_response[0].activity_name
    assert activity['client_name'] == mock_activities_response[0].client_name


@patch('services.kx_dash.KXDashService.list')
async def test_list_activities_missing_query(mock_list, auth_mock, async_client: AsyncClient, url_resolver):
    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    mock_list.return_value = []

    response = await async_client.get(url, headers=auth_mock[0])

    mock_list.assert_called_once()
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == []


@patch('services.kx_dash.KXDashService.list')
async def test_list_activities_server_error(mock_list, auth_mock, async_client: AsyncClient, url_resolver):
    mock_list.side_effect = Exception('Server error')
    url = url_resolver.reverse(operation_ids.kx_dash.LIST)

    response = await async_client.get(url, headers=auth_mock[0])

    mock_list.assert_called_once()
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to list activities'}


@patch('services.kx_dash.KXDashService.get')
async def test_get_activity_success(mock_get, auth_mock, async_client: AsyncClient, url_resolver, mock_activity):
    mock_response = DashTaskResponse.model_validate(mock_activity)
    mock_get.return_value = mock_response
    activity_id = 100001
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)

    response = await async_client.get(url, headers=auth_mock[0])

    mock_get.assert_called_once_with(activity_id)
    assert response.status_code == status.HTTP_200_OK

    data = response.json()
    assert data['activity_id'] == activity_id
    assert data['activity_name'] == mock_response.activity_name
    assert data['client_name'] == mock_response.client_name
    assert data['engagement_code'] == mock_response.engagement_code


@patch('services.kx_dash.KXDashService.get')
async def test_get_activity_not_found(mock_get, auth_mock, async_client: AsyncClient, url_resolver):
    activity_id = 999999
    mock_get.side_effect = EntityNotFoundError('Activity', str(activity_id))
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)

    response = await async_client.get(url, headers=auth_mock[0])

    mock_get.assert_called_once_with(activity_id)
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert 'not found' in response.json()['detail']


@patch('services.kx_dash.KXDashService.get')
async def test_get_activity_server_error(mock_get, auth_mock, async_client: AsyncClient, url_resolver):
    activity_id = 100001
    mock_get.side_effect = Exception('Server error')
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)

    response = await async_client.get(url, headers=auth_mock[0])

    mock_get.assert_called_once_with(activity_id)
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to retrieve activity'}


async def test_get_activity_invalid_id(auth_mock, async_client: AsyncClient, url_resolver):
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id='not-an-integer')

    response = await async_client.get(url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
