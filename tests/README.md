# Running Tests

This directory contains tests for the API-KXQualsAIassistant application.

## Prerequisites

1. Make sure you have installed the development dependencies:
   ```bash
   pip install -r requirements/dev.txt
   ```

2. Ensure the SQL Server container is running:
   ```bash
   docker-compose up -d quals-sqlserver
   ```

3. Create the test database with name "test_genai_quals" in your mssql server.

## Running Tests

To run all tests:
```bash
pytest
```

To run tests with verbose output:
```bash
pytest -v
```

To run a specific test file:
```bash
pytest tests/test_conversation_endpoints.py
pytest tests/test_message_endpoints.py
```

To run a specific test function:
```bash
pytest tests/test_conversation_endpoints.py::test_create_conversation_with_welcome_message
pytest tests/test_message_endpoints.py::test_create_message_success
```

## Test Coverage

The test suite covers the following endpoints:

### Health Check Endpoints
- GET `/health` - Basic application health check

### Conversation Endpoints
- POST `/conversations` - Create new conversation
- GET `/conversations/{id}` - Retrieve conversation by ID
- Error cases and validation

### Message Endpoints
- POST `/messages` - Create new message
- GET `/messages/{id}` - Retrieve message by ID
- Error cases including:
  - Invalid UUIDs
  - Non-existent conversations
  - Non-existent messages
  - Validation errors
  - Invalid payload formats

## Test Database

The tests use a separate test database with the prefix `test_` added to the database name specified in your environment
variables. For example, if your database is named `genai_quals`, the test database will be `test_genai_quals`.

The test database is created automatically when running the tests, but you can also create it manually using the
`create-test-db.sh` script.

## Test Configuration

Test configuration is managed through:
- `tool.pytest.ini_options in pyproject.toml` - General pytest configuration
- `conftest.py` - Test fixtures and setup
- `.env.sample` - Environment variables for testing

## Best Practices

1. Always ensure tests are isolated and don't depend on external state
2. Use fixtures from `conftest.py` for common setup
3. Follow the existing pattern for endpoint tests:
   - Test successful operations
   - Test error cases
   - Test input validation
   - Verify response structures
