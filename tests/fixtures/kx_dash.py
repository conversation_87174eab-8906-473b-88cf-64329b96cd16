from datetime import date

import pytest

from schemas import DashTaskResponse


__all__ = ['mock_activity', 'mock_activities_response']


@pytest.fixture
def mock_activity():
    """Fixture providing mock activity data"""
    return {
        'activityId': 100001,
        'activityName': 'Mock Quality Review 100001',
        'clientName': 'Mock Client Corporation',
        'memberFirm': 'US',
        'country': 'United States',
        'globalBusiness': 'Audit & Assurance',
        'globalBusinessServiceArea': 'Audit',
        'globalBusinessServiceLine': 'External Audit',
        'globalIndustry': 'Financial Services',
        'globalIndustrySector': 'Banking',
        'engagementCode': 'ENG-100001',
        'globalLCSPEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementLepEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementManagerEmails': ['<EMAIL>', '<EMAIL>'],
        'activityOwnerEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementStartDate': date(2024, 4, 1),
        'engagementEndDate': date(2024, 6, 30),
    }


@pytest.fixture
def mock_activities_response(mock_activity):
    """Fixture providing mock activities list response"""
    return [DashTaskResponse.model_validate(mock_activity)]
