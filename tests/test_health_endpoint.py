from fastapi import status
from httpx import AsyncClient

from constants.operation_ids import operation_ids


async def test_health_check(async_client: AsyncClient, url_resolver):
    url = url_resolver.reverse(operation_ids.root.HEALTH_CHECK)

    response = await async_client.get(url)

    expected = {'healthy': True, 'error': None}

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == expected
    assert response.headers['content-type'] == 'application/json'
