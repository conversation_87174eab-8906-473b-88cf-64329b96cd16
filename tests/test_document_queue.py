import json
from uuid import uuid4

from app.repositories.document_queue import DocumentQueueRepository

from config import settings
from repositories.document_blob import DocumentBlobRepository


test_queue_name = 'document-processing-test'


async def test_send_document_message_pushes_correct_message():
    blob_repo = DocumentBlobRepository(settings.document_storage.connection_string)
    upload_url = await blob_repo.upload_file(
        file_name='test.txt', content=b'Test file content', content_type='text/plain'
    )

    queue_repo = DocumentQueueRepository(settings.document_queue.connection_string, test_queue_name)
    signalr_user_id = uuid4()
    queue_message = await queue_repo.send_document_message(upload_url, signalr_user_id)
    assert queue_message is not None
    assert queue_message.id is not None
    assert queue_message.inserted_on is not None
    assert queue_message.expires_on is not None
    assert queue_message.pop_receipt is not None
    assert queue_message.content is not None
    assert queue_message.content.decode('utf-8') == json.dumps(
        {'blob_url': upload_url, 'signalr_user_id': str(signalr_user_id)}
    )


async def test_document_queue_initialise():
    queue_repo = DocumentQueueRepository(settings.document_queue.connection_string, test_queue_name)
    await queue_repo.initialize()
    assert queue_repo._initialized is True
    await queue_repo.initialize()
    assert queue_repo._initialized is True
